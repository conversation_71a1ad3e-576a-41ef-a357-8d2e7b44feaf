import { ref, watch } from "vue";
import { deductCategoryList } from "./utils";
import { cloneDeep, uniqBy } from "lodash-es";
import { getLabel, isEqualStage } from "../../utils";
import { getTrialOrderListApi } from "@telesale/server/src/api/customer/details";

interface Options {
  query: any;
  repurchaseFind: Function;
  isRepurchase: boolean;
  mode: string;
  priceDiffFind: Function;
  getOrgData?: Function;
  isExperimentGroupWorkerApi?: Function;
}

export interface TrailOrder {
  orderId: string;
  userId: string;
  deviceLock: boolean;
  trialStartTime: string;
  trialEndTime: string;
  status: string;
}

interface HeadList {
  desc: string;
  filters?: Function;
  field: string;
  minWidth?: number;
  htmlChange?: boolean;
  span?: number;
  filtersList?: any[];
  event?: string;
}

interface ActiveList {
  padType: string;
  list: any[];
}

export const usePriceDifference = (options: Options) => {
  const {
    query,
    repurchaseFind,
    isRepurchase,
    priceDiffFind,
    mode,
    getOrgData,
    isExperimentGroupWorkerApi
  } = options;
  const loading = ref(false);
  const description = ref("暂无数据");
  const dataList = ref<any[]>([]);
  const highList = ref([]);
  const highPrimaryList = ref([]);
  const initList = ref([]);
  const repurchaseType = ref("");

  const typeList = ref<any[]>([]);
  const repurchaseMap = ref({});
  const commonXugouType = ref("");
  const notXugouReason = ref([]);
  const initCommonXugouList = ref([]);

  const trialList = ref<TrailOrder[]>([]);
  const activePadList = ref<ActiveList[]>([]);
  const searchForm = ref({
    bigVipType: "",
    goodName: "",
    goodAmount: null,
    amount: undefined
  });
  const orgIds = ref([]);

  const diffHearder: HeadList[] = [
    {
      field: "goodName",
      desc: "目标商品",
      minWidth: 200,
      htmlChange: true,
      span: 2
    },
    // {
    //   field: "strategyTypeName",
    //   desc: "策略类型",
    //   minWidth: 85,
    //   span: 2
    // },
    {
      field: "amountName",
      desc: "商品售价",
      minWidth: 105
    },
    {
      field: "userAuthName",
      desc: "用户权益",
      htmlChange: true,
      minWidth: 300
    },
    {
      field: "deductCategory",
      desc: "补差方式",
      minWidth: 100,
      filters: row => getLabel(row.deductCategory, deductCategoryList)
    },
    {
      field: "discountPriceName",
      desc: "折算金额",
      event: "openDetail",
      minWidth: 85
    },
    {
      field: "diffPriceName",
      desc: "补差价",
      minWidth: 85
    }
  ];
  const activeHearder: HeadList[] = [
    {
      field: "goodName",
      desc: "商品名称",
      minWidth: 110,
      htmlChange: true,
      span: 2
    },
    {
      field: "amountName",
      desc: "商品售价",
      minWidth: 105
    },
    {
      field: "userAuthName",
      desc: "用户权益",
      htmlChange: true,
      minWidth: 300
    },
    {
      field: "discountPriceName",
      desc: "折算金额",
      event: "openDetail",
      minWidth: 85
    },
    {
      field: "diffPriceName",
      desc: "用户支付金额",
      minWidth: 85
    },
    {
      field: "endTime",
      desc: "续购资格失效时间",
      minWidth: 100
    }
  ];
  const highHearder: HeadList[] = [
    {
      field: "name",
      desc: "商品名称",
      minWidth: 110,
      span: 2
    },
    {
      field: "amount",
      desc: "商品售价",
      filters: row => row.amount?.toFixed(2)
    },
    {
      field: "paidGoodName",
      desc: "原订单名称",
      minWidth: 110,
      span: 2
    },
    {
      field: "paidAmount",
      desc: "原商品金额",
      filters: row => row.paidAmount?.toFixed(2)
    },
    {
      field: "shouldPay",
      desc: "用户支付金额",
      filters: row => row.shouldPay?.toFixed(2)
    },
    {
      field: "endTime",
      desc: "续购资格失效时间",
      minWidth: 100
    }
  ];
  const commonHearder = [
    {
      field: "commonXugouTypeText",
      desc: "续购类型",
      minWidth: 110
    },
    {
      field: "name",
      desc: "商品名称",
      minWidth: 110,
      span: 2
    },
    {
      field: "recommend",
      desc: "推荐",
      filters: row => row.recommend || "-"
    },
    {
      field: "sellingPoints",
      desc: "营销卖点",
      filters: row => row.sellingPoints || "-"
    },
    {
      field: "OriginAmount",
      desc: "商品原价",
      filters: row => row.amount?.toFixed(2)
    },
    {
      field: "amount",
      desc: "商品售价",
      filters: row => row.amount?.toFixed(2)
    }
  ];

  const listHeader = ref<HeadList[]>([]);

  function getListRepurchase(userid) {
    loading.value = true;
    dataList.value = [];
    repurchaseFind({ uid: userid, sellfrom: "telesale" })
      .then(({ data }: { data: any }) => {
        listHeader.value.push(...commonHearder);
        dataList.value = data.common;
        initCommonXugouList.value = data.common;
        data.common.forEach(item => {
          if (repurchaseMap.value[item.commonXugouType]) {
            repurchaseMap.value[item.commonXugouType].push(item);
          } else {
            repurchaseMap.value[item.commonXugouType] = [item];
            typeList.value.push({
              name: item.commonXugouTypeText,
              value: item.commonXugouType
            });
          }
        });
        const reasonList =
          data.notEntryCommonReason?.filter(item => item.layerName) || [];
        notXugouReason.value = uniqBy(reasonList, "layerName");
        commonXugouType.value = typeList.value[0]?.value;
        changeCommonXugouType(commonXugouType.value);
        repurchaseType.value = "common";
        loading.value = false;
      })
      .catch(() => {
        dataList.value = [];
        repurchaseType.value = "";
        description.value = "暂无数据";
        !query.type && (listHeader.value.length = 0);
        loading.value = false;
      });
  }

  function getList(userid) {
    if (!userid) return;
    loading.value = true;
    initList.value = [];
    activePadList.value = [];
    repurchaseType.value = "";
    trialList.value = [];
    searchForm.value = {
      bigVipType: "",
      goodName: "",
      goodAmount: null,
      amount: undefined
    };
    priceDiffFind({ userid })
      .then(async ({ data }: { data: any }) => {
        !query.type && (listHeader.value.length = 0);
        let priceDiffList = [];
        highList.value = [];
        highPrimaryList.value = [];
        if (!data.list.length) {
          description.value = "该用户不符合补差价条件";
          if (!query.type && isRepurchase) {
            getListRepurchase(userid);
          } else {
            dataList.value = [];
            loading.value = false;
            getTrial(userid);
          }
        } else {
          !query.type && listHeader.value.push(...diffHearder);
          data.list.forEach((item, index) => {
            item.strategyTypeName =
              item.strategyType === "xinxiActivityBase" ||
              item.strategyType === "xinxiActivityPro" ||
              item.strategyType === "2023double11"
                ? "新禧商品"
                : "无";
            item.amountName = "￥" + item.amount.toFixed(2);
            item.userAuthName = "";
            item.discountPrice = 0;
            const authList = [];
            item.deductibleOrders.forEach(userAuth => {
              userAuth.amountName = "￥" + userAuth.amount.toFixed(2);
              item.discountPrice += userAuth.amount;
              item.userAuthName += `<div style="color:#000;">${userAuth.name}：实际可抵扣${userAuth.amountName}</div>`;
            });
            if (!query.bigVip) {
              item.deductibleVipAuth?.forEach(deductibleVipAuth => {
                item.discountPrice += deductibleVipAuth.amount;
                item.userAuthName += `<div style="color:#000;">${
                  deductibleVipAuth.name
                }：实际可抵扣${Math.floor(deductibleVipAuth.days / 31)}个月${
                  deductibleVipAuth.days % 31
                }天,¥${deductibleVipAuth.amount.toFixed(2)}</div>`;
                authList.push(deductibleVipAuth);
              });
              item.deductibleSpecialAuth?.forEach(deductibleSpecialAuth => {
                item.discountPrice += deductibleSpecialAuth.amount;
                item.userAuthName += `<div style="color:#000;">${
                  deductibleSpecialAuth.name
                }：实际可抵扣${Math.floor(
                  deductibleSpecialAuth.days / 31
                )}个月${
                  deductibleSpecialAuth.days % 31
                }天,¥${deductibleSpecialAuth.amount.toFixed(2)}</div>`;
                authList.push(deductibleSpecialAuth);
              });
              item.deductibleAuthDetailList = authList;
              // item.deductibleAuthDetailList = mergeArray(
              //   item.deductibleAuthDetail
              // );

              // item.deductibleAuthDetailList.forEach(userAuth => {
              //   item.discountPrice += userAuth.realDeductiblePrice;
              //   if (userAuth.span > 0) {
              //     item.userAuthName += `<div style="color:#000;">${userAuth.courseName}：实际可抵扣${userAuth.deductMonth}个月${userAuth.daysNotInMonth}天,${userAuth.totalRealAmountName}</div>`;
              //   }
              // });
            }

            item.discountPriceName = "￥" + item.discountPrice.toFixed(2);
            item.diffPriceName =
              "￥" + (item.amount - item.discountPrice).toFixed(2);
            if (item.diffPriceName === "￥-0.00") {
              item.diffPriceName = "￥0.00";
            }

            item.id = index + 1;
            item.cloneName = item.goodName;
            priceDiffList.push(item);
          });

          if (query.bigVip) {
            priceDiffList = priceDiffList.filter(item => {
              return item.deductCategory === "bigVip";
            });
            listHeader.value = activeHearder;
          } else {
            priceDiffList = priceDiffList.filter(item => {
              return item.deductCategory !== "bigVip";
            });
          }

          initList.value = priceDiffList;
          dataList.value = priceDiffList;

          repurchaseType.value = "";
          loading.value = false;
          getTrial(userid);
        }
      })
      .catch(err => {
        console.log("err", err);

        !query.type && (listHeader.value.length = 0);
        description.value = "暂无数据";
        dataList.value = [];
        initList.value = [];
        repurchaseType.value = "";
        loading.value = false;
        getTrial(userid);
      });
  }

  watch(
    () => dataList.value,
    n => {
      activePadList.value = [];

      n?.forEach(item => {
        const index = activePadList.value.findIndex(
          pad => pad.padType === item.padType
        );
        if (index > -1) {
          activePadList.value[index].list.push(item);
        } else {
          activePadList.value.push({
            padType: item.padType,
            list: [item]
          });
        }
      });
    },
    {
      deep: true
    }
  );

  if (query.type === "repurchase") {
    getListRepurchase(query.userid);
  } else {
    query.type === "diffFind" && listHeader.value.push(...diffHearder);
    if (getOrgData) {
      loading.value = true;
      getOrgData()
        .then(res => {
          const ids = res.data.pathList?.reduce((pre, cur) => {
            return pre.concat(cur.id?.split?.(","));
          }, []);
          orgIds.value = [...new Set(ids)];
          if (!query.userid) {
            loading.value = false;
            return;
          }
          getList(query.userid);
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      getList(query.userid);
    }
  }

  //表头筛选
  function filterChange(row) {
    if (initList.value.length > 0) {
      searchForm.value.goodAmount = row?.amountName?.[0] ?? null;
      filterData();
      return;
    }
    if (!row.amountName[0]) {
      dataList.value = initList.value;
    } else {
      const arr = row.amountName[0].split("-");
      dataList.value = initList.value.filter(item => {
        if (!arr[1]) {
          return item.amount >= arr[0];
        } else {
          return item.amount >= arr[0] && item.amount < arr[1];
        }
      });
    }
  }

  const resetData = () => {
    searchForm.value = {
      bigVipType: searchForm.value.bigVipType,
      goodName: "",
      goodAmount: null,
      amount: undefined
    };
    filterData();
  };

  const filterData = () => {
    const name = searchForm.value.goodName;
    const type = searchForm.value.bigVipType;
    const amount = searchForm.value.amount;

    let init = cloneDeep(initList.value);
    let arr = searchForm.value.goodAmount;
    if (arr) {
      arr = arr.split("-");
      init = init.filter(item => {
        if (!arr[1]) {
          return item.amount >= arr[0];
        } else {
          return item.amount >= arr[0] && item.amount < arr[1];
        }
      });
    }

    if (amount || amount === 0) {
      init = init.filter(item => item.amount === Number(amount));
    }

    if (!name && !type) return (dataList.value = init);
    const list = init.filter(item => {
      if (name) {
        item.goodName = item.goodName.replace(
          new RegExp(name, "g"),
          `<span style="color: red">${name}</span>`
        );
      }
      if (!name) {
        return isEqualStage(item.targetUserStage, type);
      }
      if (!type) {
        return item.goodName.includes(name);
      }

      return (
        item.goodName.includes(name) && isEqualStage(item.targetUserStage, type)
      );
    });
    dataList.value = list;
  };

  const getEndTime = list => {
    let time = "";
    list?.forEach(item => {
      if (item.endTime) {
        if (time) {
          time =
            new Date(time).getTime() > new Date(item.endTime).getTime()
              ? time
              : item.endTime;
        } else {
          time = item.endTime;
        }
      }
    });
    return time;
  };

  const getTrial = (id: string) => {
    loading.value = true;
    getTrialOrderListApi({ userId: id })
      .then(res => {
        trialList.value = res.data.trialOrderInfos || [];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const changeCommonXugouType = async (e: string) => {
    if (!e) {
      dataList.value = initCommonXugouList.value;
      return;
    }
    dataList.value = repurchaseMap.value[e];
  };

  return {
    loading,
    description,
    dataList,
    highList,
    highPrimaryList,
    initList,
    repurchaseType,
    searchForm,
    diffHearder,
    activeHearder,
    highHearder,
    commonHearder,
    listHeader,
    trialList,
    activePadList,
    typeList,
    commonXugouType,
    notXugouReason,
    changeCommonXugouType,
    getListRepurchase,
    getList,
    filterChange,
    filterData,
    getEndTime,
    resetData
  };
};
