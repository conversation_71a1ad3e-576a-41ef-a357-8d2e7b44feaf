<!--
 * @Date         : 2025-01-22 14:15:25
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<!--
 * @Date         : 2024-07-18 16:37:46
 * @Description  : 新增编辑视频
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { DiffPriceInfo } from "/@/api/customer/diffPrice";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";
import { deductCategoryList } from "../data/index";

interface Props {
  value: boolean;
  data: DiffPriceInfo["deductibleOrders"];
  maxDeductAmount: number;
  showSum: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const dataList = computed(() => {
  if (!props.showSum) {
    return props.data;
  }
  return props.data?.filter(item => item.deductCategory !== "direct");
});

const columns: TableColumns[] = [
  {
    field: "id",
    desc: "订单ID",
    customRender: ({ row, text }) => {
      return row.deductCategory === "direct" ? "-" : text;
    }
  },
  {
    field: "name",
    desc: "订单名称",
    customRender: ({ row, text }) => {
      return row.deductCategory === "direct" ? "-" : text;
    }
  },
  {
    field: "paidTime",
    desc: "支付时间",
    timeChange: 2
  },
  {
    field: "deductAmount",
    desc: "抵扣金额",
    customRender: ({ text }) => {
      return `¥${text}`;
    }
  },
  {
    field: "deductCategory",
    desc: "抵扣类型",
    customRender: ({ text }) => {
      return getLabel(text, deductCategoryList);
    }
  },
  {
    field: "strategyName",
    desc: "策略名称",
    customRender: ({ text }) => {
      return text || "-";
    }
  },
  {
    field: "sellingPoints",
    desc: "营销卖点",
    customRender: ({ text }) => {
      return text || "-";
    }
  },
  {
    field: "remark",
    desc: "备注"
  }
];

const handleClose = () => {
  isModel.value = false;
};
</script>

<template>
  <el-dialog title="补差抵扣详情" v-model="isModel" :before-close="handleClose">
    <div class="c-red font-bold mb-10px">
      活动规则：补差价直降优惠+7天外订单抵扣优惠之和不超过¥{{
        props.maxDeductAmount
      }}
    </div>
    <div>
      <ReTable :dataList="dataList" :listHeader="columns" />
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>
