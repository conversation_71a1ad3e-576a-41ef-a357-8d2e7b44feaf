<script setup lang="ts">
import { ref, computed, watch } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import Operation from "./components/Operation.vue";
import { priceDiffFind, repurchaseFind } from "/@/api/customerDetails";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStore } from "/@/store/modules/user";
import { useRenderIcon } from "../ReIcon/src/hooks";
import AuthDetail from "./dialog/AuthDetail.vue";
import { usePriceDifference } from "@telesale/shared";
import { bigVipList } from "@telesale/shared/src/data/customer";
import { getAuth } from "/@/utils/auth";
import { getTrialOrderListApi, TrailOrder } from "/@/api/customer/details";
import { storeToRefs } from "pinia";
import { findPosition } from "/@/api/agent";
import { isExperimentGroupWorkerApi } from "/@/api/system/distribute";

let device = useAppStoreHook().device;
interface Props {
  userid?: string;
  loading?: boolean;
  isShowOperation: boolean;
  type?: string;
  bigVip?: boolean;
}

interface Emits {
  (e: "update:loading", val: boolean): void;
}

const emit = defineEmits<Emits>();

const props = defineProps<Props>();

const { userMsg } = storeToRefs(useUserStore());

const loading = computed({
  get() {
    return props.loading;
  },
  set(val: boolean) {
    emit("update:loading", val);
  }
});

let isRepurchase = getAuth("telesale_admin_repurchase");
const mode = import.meta.env.MODE;

let isModel = ref<boolean>(false);
let dataMemery = ref();
let authDetailRef = ref();
const tableRefs = ref();
const riskSpreadRefList = ref([]);
const getRiskSpreadRef = (el, index) => {
  if (el) {
    riskSpreadRefList.value[index] = el;
  }
};
function resetFitler() {
  riskSpreadRefList.value?.forEach(el => {
    el?.resetFilter?.();
  });
}

const reset = () => {
  resetFitler();
  resetData();
};

const {
  loading: useLoading,
  description,
  dataList,
  highList,
  highPrimaryList,
  initList,
  repurchaseType,
  searchForm,
  highHearder,
  listHeader,
  activePadList,
  getList,
  resetData,
  filterChange,
  filterData,
  getEndTime,
  changeCommonXugouType,
  commonXugouType,
  notXugouReason,
  typeList,
  trialList
} = usePriceDifference({
  query: props,
  repurchaseFind,
  isRepurchase,
  priceDiffFind,
  mode,
  getOrgData: () => findPosition({ key: "id", value: userMsg.value.id + "" }),
  isExperimentGroupWorkerApi: () =>
    isExperimentGroupWorkerApi({ workerId: userMsg.value.id })
});

function openDetail(row) {
  dataMemery.value = row;
  isModel.value = true;
}

function parantMath({ key, params }) {
  switch (key) {
    case "openDetail":
      openDetail(params);
      break;
  }
}

watch(
  () => useLoading.value,
  n => {
    loading.value = n;
  },
  {
    deep: true,
    immediate: true
  }
);

defineExpose({
  getList,
  listHeader,
  description,
  dataList,
  initList,
  highList,
  highPrimaryList,
  repurchaseType,
  trialList
});
</script>

<template>
  <div v-loading="useLoading" class="min-h-100px">
    <template v-if="initList.length > 0">
      <el-form :model="searchForm" inline @submit.prevent>
        <el-form-item label="">
          <el-input
            v-model="searchForm.goodName"
            clearable
            placeholder="请输入商品名称"
            @keyup.enter="filterData"
          />
        </el-form-item>
        <el-form-item label="">
          <el-input-number
            v-model="searchForm.amount"
            clearable
            placeholder="请输入商品价格"
            style="width: 200px"
            @keyup.enter="filterData"
          />
        </el-form-item>
        <el-form-item label="">
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            @click="filterData"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon('refresh')" @click="reset">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-radio-group v-model="searchForm.bigVipType" @change="filterData">
        <el-radio
          v-for="(item, index) in bigVipList"
          :key="index"
          :label="item.value"
        >
          {{ item.label }}
        </el-radio>
      </el-radio-group>
    </template>
    <div v-if="trialList?.length > 0">
      <div
        class="c-red flex gap-20px mb-5px"
        v-for="(item, index) in trialList"
        :key="index"
      >
        <span>体验机订单：{{ item.orderId }}</span>
        <span>
          体验周期：{{ item.trialStartTime }}至 {{ item.trialEndTime }}
        </span>
        <span>是否锁机：{{ item.deviceLock ? "是" : "否" }}</span>
      </div>
    </div>
    <template v-if="type !== 'repurchase' || repurchaseType">
      <div
        class="text-18px mb-10px c-red"
        v-if="getEndTime(dataList) && props.bigVip"
      >
        续购资格失效时间：{{ getEndTime(dataList) }}
      </div>
      <template v-if="type !== 'repurchase'">
        <template v-for="(item, index) in activePadList" :key="index">
          <div class="text-18px font-bold my-10px">
            {{ item.padType ? `带${item.padType}` : "不带" }}平板商品：
          </div>
          <ReTable
            :ref="el => getRiskSpreadRef(el, index)"
            :dataList="item.list"
            :listHeader="listHeader"
            :filterChange="filterChange"
            :description="description"
            rowKey="id"
            @parantMath="parantMath"
          >
            <template #appendColumn>
              <el-table-column
                fixed="right"
                label="操作"
                width="150"
                v-if="isShowOperation"
              >
                <template #default="scope">
                  <Operation
                    v-if="!scope.row.children"
                    :row="scope.row"
                    :index="scope.$index"
                    :type="type"
                    :repurchaseType="repurchaseType"
                  />
                </template>
              </el-table-column>
            </template>
          </ReTable>
        </template>
        <template v-if="activePadList.length === 0">
          <el-empty description="暂无数据" />
        </template>
      </template>
      <template v-else>
        <div v-if="notXugouReason.length">
          <div v-for="item in notXugouReason" :key="item.strategyId">
            {{ item.layerResult ? "满足" : "不满足" }}：
            {{ item.layerName }}
          </div>
        </div>
        <div class="flex items-center">
          续购类型:
          <el-radio-group
            v-model="commonXugouType"
            @change="changeCommonXugouType"
            class="ml-20px mt-2px"
          >
            <el-radio
              v-for="item in typeList"
              :key="item.value"
              :label="item.value"
            >
              {{ item.name }}
            </el-radio>
          </el-radio-group>
        </div>
        <ReTable
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :filterChange="filterChange"
          :description="description"
          rowKey="id"
          @parantMath="parantMath"
        >
          <template #appendColumn>
            <el-table-column
              fixed="right"
              label="操作"
              width="150"
              v-if="isShowOperation"
            >
              <template #default="scope">
                <Operation
                  v-if="!scope.row.children"
                  :row="scope.row"
                  :index="scope.$index"
                  :type="type"
                  :repurchaseType="repurchaseType"
                />
              </template>
            </el-table-column>
          </template>
        </ReTable>
      </template>
    </template>

    <AuthDetail
      ref="authDetailRef"
      v-if="isModel"
      v-model:value="isModel"
      :dataMemery="dataMemery"
    />
  </div>
</template>
<style scoped>
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}

h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 20px 0;
}
:deep(.el-form-item .el-input-number .el-input) {
  width: 200px;
}
</style>
