/*
 * @Date         : 2024-03-27 14:36:11
 * @Description  :资料管理
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ElMessage } from "element-plus";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import { gradeList } from "/@/utils/data/common";
import { callStatusList } from "../../ongoing/utils/list";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getLabel } from "/@/utils/common";
import { clueSourceTree, roleList } from "@telesale/shared/src/data/customer";
import timeChange from "/@/utils/handle/timeChange";
import stageList from "/@/utils/data/stageList";
import { ref } from "vue";
import { durationChangeMath } from "../../ongoing/utils/listHeader";
import sourceObj from "/@/utils/data/sourceObj";
import filterDataChange from "/@/utils/handle/filterDataChange";
import { getAuth } from "/@/utils/auth";
import {
  familyCategoryList,
  familyCategoryListMap,
  studyInfoBindList,
  studyInfoBindListMap
} from "/@/utils/const/customer";
import durationChange from "/@/utils/handle/durationChange";

const { intentionList, userMsg, allAgentObj, orderTeams } = storeToRefs(
  useUserStore()
);

const isList = [
  { label: "是", value: true },
  { label: "否", value: false }
];

export enum TabEnum {
  /** 998已升单 */
  upOrder = 2,
  /** 998未升单 */
  notUpOrder = 3,
  /** 大会员 */
  big_vip = 4,
  /** 零售商品 */
  retailGoods = 5,
  /** 体验课 */
  experience_course = 6,
  /** 分层测试商品 */
  testGoods = 18,
  /** 蓄水商品 */
  xushuiGoods = 20,
  /** 暑促定金 */
  summerDeposit = 21,
  /** 暑促体验机 */
  summerExperience = 22
}

export type TabType = TabEnum;

export const tabList = [
  {
    label: "998未升单",
    value: TabEnum.notUpOrder,
    auth: "telesale_admin_custom_pay_clue_notUpOrder",
    payCategory: [TabEnum.notUpOrder],
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "998已升单",
    value: TabEnum.upOrder,
    auth: "telesale_admin_custom_pay_clue_upOrder",
    payCategory: [TabEnum.upOrder],
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "零售商品",
    value: TabEnum.retailGoods,
    auth: "telesale_admin_custom_pay_clue_retailGoods",
    payCategory: [TabEnum.retailGoods],
    excludeTags: [
      TabEnum.testGoods,
      TabEnum.xushuiGoods,
      TabEnum.summerDeposit,
      TabEnum.summerExperience
    ]
  },
  {
    label: "体验课",
    value: TabEnum.experience_course,
    auth: "telesale_admin_custom_pay_clue_experienceCourse",
    payCategory: [TabEnum.experience_course],
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "大会员",
    value: TabEnum.big_vip,
    auth: "telesale_admin_custom_pay_clue_bigVip",
    payCategory: [TabEnum.big_vip],
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "分层测试商品",
    value: TabEnum.testGoods,
    auth: "telesale_admin_custom_pay_clue_testGood",
    featureTag: TabEnum.testGoods,
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "蓄水商品",
    value: TabEnum.xushuiGoods,
    auth: "telesale_admin_custom_pay_clue_xushuiGood",
    featureTag: TabEnum.xushuiGoods,
    excludeTags: [TabEnum.summerDeposit, TabEnum.summerExperience]
  },
  {
    label: "暑促定金",
    value: TabEnum.summerDeposit,
    auth: "telesale_admin_custom_pay_clue_deposit",
    featureTag: TabEnum.summerDeposit,
    excludeTags: [TabEnum.summerExperience]
  },
  {
    label: "暑促体验机",
    value: TabEnum.summerExperience,
    auth: "telesale_admin_custom_pay_notDifference",
    featureTag: TabEnum.summerExperience
  }
];

export const isDealList = [
  { label: "未成交", value: false },
  { label: "已成交", value: true }
];

const isFamily = getAuth("telesale_admin_customer_family_detail");

export const getColumnsList = (tabType: TabType): TableColumns[] => {
  return [
    {
      field: "familyId",
      desc: "家庭ID",
      isFamily: true,
      minWidth: 115,
      isCopy: true,
      fixed: true,
      addType: "up",
      isShow: () => getAuth("telesale_admin_customer_family_detail")
    },
    {
      field: "onionid",
      desc: "洋葱ID",
      event: "openCustomerDrawer",
      minWidth: 115,
      isCopy: true,
      fixed: true
    },
    {
      field: "phone",
      desc: "客户手机号",
      minWidth: 130,
      isCopy: true,
      fixed: true
    },
    {
      field: "upgradeDiscountDeadline",
      desc: "升单优惠截止时间",
      isTimeSort: true,
      timeChange: 2,
      minWidth: 170,
      sortable: true,
      isShow: () => tabType === TabEnum.notUpOrder
    },
    {
      field: "lastActiveTime",
      desc: "最近一次看课时间",
      isTimeSort: true,
      timeChange: 2,
      minWidth: 170,
      sortable: true
    },
    {
      field: "learnLength",
      desc: "过去30天学习时长",
      timeChange: 2,
      minWidth: 130,
      sortable: true,
      customRender: ({ text }) => {
        return text ? durationChange(text) : "";
      }
    },
    {
      field: "lastDealing",
      desc: "最近一次拨通时间",
      isTimeSort: true,
      timeChange: 2,
      minWidth: 130,
      sortable: true
    },
    {
      field: "historyAmount",
      desc: "历史付费金额总和",
      isTimeSort: true,
      headerTip: true,
      minWidth: 115,
      sortable: true
    },
    {
      field: "authEndAt",
      desc: "权益到期时间",
      timeChange: 2,
      minWidth: 130,
      sortable: true
    },
    {
      field: "lastPaidTime",
      desc: "最近一次付费时间",
      isTimeSort: true,
      timeChange: 2,
      minWidth: 170,
      sortable: true
    },
    {
      field: "description",
      desc: "用户信息记录",
      minWidth: 160,
      customRender: ({ text }) => {
        return (
          <el-tooltip
            popper-class="max-w-700px"
            effect="dark"
            content={text}
            placement="top"
          >
            <div class="w-120px line-clamp-3 ">{text}</div>
          </el-tooltip>
        );
      }
    },
    {
      field: "role",
      desc: "用户身份",
      minWidth: 110,
      filterOptions: {
        columns: roleList
      },
      customRender: ({ text }) => {
        return getLabel(text, roleList);
      }
    },
    {
      field: "grade",
      desc: "年级",
      minWidth: 75,
      filterOptions: {
        columns: gradeList,
        isMultiple: true,
        label: "text"
      }
    },
    {
      field: "userExpire",
      desc: "线索到期时间",
      isTimeSort: true,
      timeChange: 2,
      minWidth: 140,
      sortable: true
    },
    {
      field: "intention",
      desc: "意向度",
      minWidth: 85,
      typeChange: intentionList.value,
      filterOptions: {
        columns: intentionList,
        isMultiple: true,
        label: "text"
      }
    },
    {
      field: "callCount",
      desc: "外呼次数",
      isTimeSort: true,
      minWidth: 110,
      sortable: true
    },
    {
      field: "callState",
      desc: "外呼状态",
      minWidth: 100,
      typeChange: callStatusList,
      filterOptions: {
        columns: callStatusList,
        isMultiple: true,
        label: "text"
      }
    },
    {
      field: "source",
      desc: "来源",
      typeChange: sourceObj,
      minWidth: 90,
      filterCascaderOptions: {
        columns: clueSourceTree
      }
    },
    {
      field: "workerName",
      desc: "坐席名称",
      minWidth: 85,
      idTransfer: "name",
      isShow: () => userMsg.value.leafNode
    },

    {
      field: "openCount",
      desc: "打开学情报告次数",
      minWidth: 110,
      sortable: true,
      addType: "up"
    },
    {
      field: "studyInfoBindType",
      desc: "学情被绑定状态",
      minWidth: 110,
      addType: "up",
      filterOptions: {
        columns: studyInfoBindList,
        isMultiple: true
      },
      customRender: ({ text }) => {
        return studyInfoBindListMap[text];
      },
      isShow: () => getAuth("telesale_admin_customer_family_detail")
    },
    {
      field: "familyCategory",
      desc: "所属家庭类型",
      minWidth: 110,
      addType: "up",
      filterOptions: {
        columns: familyCategoryList,
        isMultiple: true
      },
      customRender: ({ text }) => {
        return familyCategoryListMap[text];
      },
      isShow: () => getAuth("telesale_admin_customer_family_detail")
    },
    {
      field: "lastDial",
      desc: "最近一次拨打时间",
      isTimeSort: true,
      filters: row => {
        if (!row.lastDial) {
          return "暂无呼叫记录";
        }
        return row.lastDial < 253370764800
          ? timeChange(row.lastDial, 2)
          : "暂无呼叫记录";
      },
      headerTip: true,
      minWidth: 130,
      sortable: true
    },

    {
      field: "stage",
      desc: "学段",
      minWidth: 75,
      filterOptions: {
        columns: stageList,
        isMultiple: true,
        label: "text"
      }
    },

    {
      field: "newExam",
      desc: "是否新题型",
      typeChange: isList,
      filtersList: filterDataChange(isList, "label", "value"),
      minWidth: 105,
      customRender: ({ text }) => {
        return text ? "是" : "否";
      }
    },
    {
      field: "createdAt",
      desc: "创建时间",
      isTimeSort: true,
      sortable: true,
      timeChange: 1,
      headerTip: true,
      minWidth: 100
    },
    {
      field: "firstDialDuration",
      desc: "首次呼叫间隔",
      filters: durationChangeMath,
      filtersList: [
        {
          text: "暂无呼叫记录",
          value: true
        }
      ],
      headerTip: true,
      minWidth: 145
    },
    {
      field: "order998Teams",
      desc: "998订单业务归属",
      minWidth: 150,
      addType: "down",
      typeChange: orderTeams.value,
      filterOptions: {
        columns: orderTeams,
        isMultiple: true,
        label: "name",
        value: "teamId"
      },
      customRender: ({ text }) => {
        return text
          ?.map(item => getLabel(item, orderTeams.value, "name", "teamId"))
          .join("、");
      },
      isShow: () => tabType === TabEnum.notUpOrder
    }
  ];
};

export const hideFiled = [
  "openCount",
  "studyInfoBindType",
  "familyCategory",
  "lastDial",
  "stage",
  "newExam",
  "createdAt",
  "firstDialDuration",
  "order998Teams"
];

export const operation = (toDetail: Function): OperationObj[] => {
  return [
    {
      text: "一键外呼",
      eventFn: params => {
        if (!params.phone) {
          ElMessage.warning("此线索没有手机号");
          return;
        }
        toDetail(params, "PayClue", 1);
      }
    },
    {
      text: "查看详情",
      eventFn: params => {
        toDetail(params, "PayClue", "");
      }
    },
    {
      text: "收藏线索",
      isShow: row =>
        row.workerid === userMsg.value.id &&
        getAuth("telesale_admin_custom_collect_clue"),
      event: "collect"
    }
  ];
};
